# Production Environment Configuration
server:
  port: 80
  address: 0.0.0.0  # Listen on all network interfaces
  servlet:
    context-path: /
  compression:
    enabled: true
  tomcat:
    max-threads: 500
    min-spare-threads: 20
    accept-count: 100
    max-connections: 8192

spring:
  # JSON Configuration
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
      indent-output: false  # Production environment doesn't need formatted output

# Mail Configuration (Production)
mail:
  # POP3 Server Configuration
  pop3-host: pop.126.com
  pop3-port: 995
  pop3-ssl: true
  
  # SMTP Server Configuration
  smtp-host: smtp.126.com
  smtp-port: 465
  smtp-ssl: true
  
  # Transfer Email Configuration (Production environment recommends setting via environment variables)
  transfer-email: ${MAIL_TRANSFER_EMAIL:<EMAIL>}
  transfer-password: ${MAIL_TRANSFER_PASSWORD:TQVTv7gUbCGy9eBR}
  
  # User Domain Configuration
  user-domain: ${MAIL_USER_DOMAIN:shengchai.dpdns.org}
  
  # Connection Timeout Configuration
  connection-timeout: 15000
  read-timeout: 15000

# Logging Configuration (Production)
logging:
  level:
    root: WARN
    com.mailservice: INFO
    org.springframework: WARN
    javax.mail: ERROR
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/temp-mail-server/app.log
    max-size: 200MB
    max-history: 60
  logback:
    rollingpolicy:
      max-file-size: 200MB
      total-size-cap: 5GB

# Management Endpoints Configuration (Production)
management:
  endpoints:
    web:
      exposure:
        include: health,info
      base-path: /actuator
  endpoint:
    health:
      show-details: never
  server:
    port: 8080  # Management port separated from application port

# Application Information
info:
  app:
    name: Temporary Mail Service
    description: Production Environment Temporary Mail Service
    version: 1.0.0
    environment: production
