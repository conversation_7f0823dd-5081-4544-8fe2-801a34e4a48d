- "BOOT-INF/lib/spring-boot-2.7.14.jar"
- "BOOT-INF/lib/spring-boot-autoconfigure-2.7.14.jar"
- "BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar"
- "BOOT-INF/lib/spring-core-5.3.29.jar"
- "BOOT-INF/lib/spring-jcl-5.3.29.jar"
- "BOOT-INF/lib/snakeyaml-1.30.jar"
- "BOOT-INF/lib/jackson-datatype-jdk8-2.13.5.jar"
- "BOOT-INF/lib/jackson-datatype-jsr310-2.13.5.jar"
- "BOOT-INF/lib/jackson-module-parameter-names-2.13.5.jar"
- "BOOT-INF/lib/tomcat-embed-core-9.0.78.jar"
- "BOOT-INF/lib/tomcat-embed-websocket-9.0.78.jar"
- "BOOT-INF/lib/spring-web-5.3.29.jar"
- "BOOT-INF/lib/spring-beans-5.3.29.jar"
- "BOOT-INF/lib/spring-webmvc-5.3.29.jar"
- "BOOT-INF/lib/spring-aop-5.3.29.jar"
- "BOOT-INF/lib/spring-context-5.3.29.jar"
- "BOOT-INF/lib/spring-expression-5.3.29.jar"
- "BOOT-INF/lib/tomcat-embed-el-9.0.78.jar"
- "BOOT-INF/lib/hibernate-validator-6.2.5.Final.jar"
- "BOOT-INF/lib/jakarta.validation-api-2.0.2.jar"
- "BOOT-INF/lib/jboss-logging-3.4.3.Final.jar"
- "BOOT-INF/lib/classmate-1.5.1.jar"
- "BOOT-INF/lib/javax.mail-1.6.2.jar"
- "BOOT-INF/lib/activation-1.1.jar"
- "BOOT-INF/lib/jackson-databind-2.13.5.jar"
- "BOOT-INF/lib/jackson-annotations-2.13.5.jar"
- "BOOT-INF/lib/jackson-core-2.13.5.jar"
- "BOOT-INF/lib/logback-classic-1.2.12.jar"
- "BOOT-INF/lib/logback-core-1.2.12.jar"
- "BOOT-INF/lib/slf4j-api-1.7.36.jar"
- "BOOT-INF/lib/log4j-to-slf4j-2.17.2.jar"
- "BOOT-INF/lib/log4j-api-2.17.2.jar"
- "BOOT-INF/lib/jul-to-slf4j-1.7.36.jar"
- "BOOT-INF/lib/spring-boot-jarmode-layertools-2.7.14.jar"
