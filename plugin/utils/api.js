/**
 * API通信工具模块
 * 负责与后端服务器的所有API交互
 */

// API配置
const API_CONFIG = {
    BASE_URL: 'http://1.12.224.176',
    API_KEY: 'temp-mail-plugin-2024', // 硬编码API密钥（按需求文档要求）
    TIMEOUT: 15000, // 15秒超时
    RETRY_COUNT: 3 // 重试次数
};

// API端点
const API_ENDPOINTS = {
    HEALTH: '/api/health',
    GENERATE_EMAIL: '/api/generate-email',
    GET_EMAILS: '/api/get-emails',
    GET_SHARED_RULES: '/api/get-shared-rules',
    UPLOAD_SHARED_RULE: '/api/upload-shared-rule',
    CLEANUP_SESSION: '/api/cleanup-session',
    GET_STATUS: '/api/get-status'
};

/**
 * 通用API请求函数
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API响应
 */
async function apiRequest(endpoint, options = {}) {
    const url = `${API_CONFIG.BASE_URL}${endpoint}`;
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-API-Key': API_CONFIG.API_KEY,
            'User-Agent': 'TempMailPlugin/1.0.0'
        },
        timeout: API_CONFIG.TIMEOUT
    };

    const requestOptions = { ...defaultOptions, ...options };
    
    // 如果有body数据，转换为JSON字符串
    if (requestOptions.body && typeof requestOptions.body === 'object') {
        requestOptions.body = JSON.stringify(requestOptions.body);
    }

    let lastError;
    
    // 重试机制
    for (let attempt = 1; attempt <= API_CONFIG.RETRY_COUNT; attempt++) {
        try {
            console.log(`API请求 (尝试 ${attempt}/${API_CONFIG.RETRY_COUNT}):`, url, requestOptions);
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT);
            
            const response = await fetch(url, {
                ...requestOptions,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('API响应:', data);
            
            return data;
            
        } catch (error) {
            lastError = error;
            console.error(`API请求失败 (尝试 ${attempt}/${API_CONFIG.RETRY_COUNT}):`, error);
            
            // 如果不是最后一次尝试，等待后重试
            if (attempt < API_CONFIG.RETRY_COUNT) {
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
    
    throw new Error(`API请求最终失败: ${lastError.message}`);
}

/**
 * 健康检查
 * @returns {Promise<Object>} 健康状态
 */
async function checkHealth() {
    return await apiRequest(API_ENDPOINTS.HEALTH);
}

/**
 * 生成临时邮箱
 * @param {Object} emailRequest - 邮箱请求数据
 * @returns {Promise<Object>} 生成结果
 */
async function generateEmail(emailRequest) {
    return await apiRequest(API_ENDPOINTS.GENERATE_EMAIL, {
        method: 'POST',
        body: emailRequest
    });
}

/**
 * 获取邮件列表
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 邮件列表
 */
async function getEmails(userId) {
    const endpoint = `${API_ENDPOINTS.GET_EMAILS}?userId=${encodeURIComponent(userId)}`;
    return await apiRequest(endpoint);
}

/**
 * 获取共享解析规则
 * @returns {Promise<Object>} 共享规则列表
 */
async function getSharedRules() {
    return await apiRequest(API_ENDPOINTS.GET_SHARED_RULES);
}

/**
 * 上传共享解析规则
 * @param {Object} rule - 解析规则
 * @returns {Promise<Object>} 上传结果
 */
async function uploadSharedRule(rule) {
    return await apiRequest(API_ENDPOINTS.UPLOAD_SHARED_RULE, {
        method: 'POST',
        body: rule
    });
}

/**
 * 清理用户会话
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 清理结果
 */
async function cleanupSession(userId) {
    const endpoint = `${API_ENDPOINTS.CLEANUP_SESSION}?userId=${encodeURIComponent(userId)}`;
    return await apiRequest(endpoint, {
        method: 'DELETE'
    });
}

/**
 * 获取服务器状态
 * @returns {Promise<Object>} 服务器状态
 */
async function getServerStatus() {
    return await apiRequest(API_ENDPOINTS.GET_STATUS);
}

// 导出API函数
window.TempMailAPI = {
    checkHealth,
    generateEmail,
    getEmails,
    getSharedRules,
    uploadSharedRule,
    cleanupSession,
    getServerStatus,
    config: API_CONFIG
};
