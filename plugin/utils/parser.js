/**
 * 邮件解析工具模块
 * 负责从邮件内容中提取验证码和链接
 */

// 内置验证码匹配模式
const BUILTIN_CODE_PATTERNS = [
    // 数字验证码
    /(?:验证码|verification code|code|验证|激活码|动态码)[\s\S]*?(\d{4,8})/gi,
    /(\d{6})\s*(?:是您的|为您的|is your|verification|code)/gi,
    /(?:您的|your)\s*(?:验证码|code)[\s\S]*?(\d{4,8})/gi,
    // 字母数字混合验证码
    /(?:验证码|verification code|code)[\s\S]*?([A-Z0-9]{4,12})/gi,
    /([A-Z0-9]{6,8})\s*(?:是您的|为您的|is your|verification)/gi
];

// 内置链接匹配模式
const BUILTIN_LINK_PATTERNS = [
    // 激活链接
    /(?:激活|activate|confirm|verify)[\s\S]*?(https?:\/\/[^\s<>"']+)/gi,
    /(?:点击|click)[\s\S]*?(https?:\/\/[^\s<>"']+)/gi,
    // 重置密码链接
    /(?:重置|reset|password)[\s\S]*?(https?:\/\/[^\s<>"']+)/gi,
    // 通用链接
    /(https?:\/\/[^\s<>"']+(?:verify|confirm|activate|reset|login)[^\s<>"']*)/gi
];

/**
 * 解析规则数据结构
 * {
 *   id: string,
 *   senderEmail: string,
 *   name: string,
 *   codePattern: string,
 *   linkPattern: string,
 *   isShared: boolean,
 *   createdAt: string,
 *   updatedAt: string
 * }
 */

/**
 * 从邮件内容中提取验证码
 * @param {string} content - 邮件内容
 * @param {Array} customPatterns - 自定义匹配模式
 * @returns {Array} 提取到的验证码列表
 */
function extractCodes(content, customPatterns = []) {
    const codes = new Set();
    const patterns = [...customPatterns, ...BUILTIN_CODE_PATTERNS];
    
    patterns.forEach(pattern => {
        try {
            const regex = typeof pattern === 'string' ? new RegExp(pattern, 'gi') : pattern;
            const matches = content.matchAll(regex);
            
            for (const match of matches) {
                // 获取捕获组中的验证码
                const code = match[1] || match[0];
                if (code && /^[A-Z0-9]{4,12}$/i.test(code)) {
                    codes.add(code);
                }
            }
        } catch (error) {
            console.warn('验证码匹配模式错误:', pattern, error);
        }
    });
    
    return Array.from(codes);
}

/**
 * 从邮件内容中提取链接
 * @param {string} content - 邮件内容
 * @param {Array} customPatterns - 自定义匹配模式
 * @returns {Array} 提取到的链接列表
 */
function extractLinks(content, customPatterns = []) {
    const links = new Set();
    const patterns = [...customPatterns, ...BUILTIN_LINK_PATTERNS];
    
    patterns.forEach(pattern => {
        try {
            const regex = typeof pattern === 'string' ? new RegExp(pattern, 'gi') : pattern;
            const matches = content.matchAll(regex);
            
            for (const match of matches) {
                // 获取捕获组中的链接
                const link = match[1] || match[0];
                if (link && isValidUrl(link)) {
                    links.add(link);
                }
            }
        } catch (error) {
            console.warn('链接匹配模式错误:', pattern, error);
        }
    });
    
    return Array.from(links);
}

/**
 * 验证URL是否有效
 * @param {string} url - URL字符串
 * @returns {boolean} 是否有效
 */
function isValidUrl(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
        return false;
    }
}

/**
 * 根据发件人查找匹配的解析规则
 * @param {string} senderEmail - 发件人邮箱
 * @param {Array} localRules - 本地解析规则
 * @param {Array} sharedRules - 共享解析规则
 * @returns {Object|null} 匹配的解析规则
 */
function findMatchingRule(senderEmail, localRules = [], sharedRules = []) {
    // 优先使用本地规则
    const localRule = localRules.find(rule => 
        rule.senderEmail && rule.senderEmail.toLowerCase() === senderEmail.toLowerCase()
    );
    
    if (localRule) {
        console.log('找到本地解析规则:', localRule);
        return localRule;
    }
    
    // 使用共享规则
    const sharedRule = sharedRules.find(rule => 
        rule.senderEmail && rule.senderEmail.toLowerCase() === senderEmail.toLowerCase()
    );
    
    if (sharedRule) {
        console.log('找到共享解析规则:', sharedRule);
        return sharedRule;
    }
    
    console.log('未找到匹配的解析规则:', senderEmail);
    return null;
}

/**
 * 解析单封邮件
 * @param {Object} email - 邮件对象
 * @param {Array} localRules - 本地解析规则
 * @param {Array} sharedRules - 共享解析规则
 * @returns {Object} 解析结果
 */
function parseEmail(email, localRules = [], sharedRules = []) {
    const result = {
        email: email,
        codes: [],
        links: [],
        hasRule: false,
        usedRule: null,
        parseTime: new Date().toISOString()
    };
    
    if (!email.content) {
        return result;
    }
    
    // 查找匹配的解析规则
    const matchingRule = findMatchingRule(email.from, localRules, sharedRules);
    
    if (matchingRule) {
        result.hasRule = true;
        result.usedRule = matchingRule;
        
        // 使用自定义规则解析
        const customCodePatterns = matchingRule.codePattern ? [matchingRule.codePattern] : [];
        const customLinkPatterns = matchingRule.linkPattern ? [matchingRule.linkPattern] : [];
        
        result.codes = extractCodes(email.content, customCodePatterns);
        result.links = extractLinks(email.content, customLinkPatterns);
    } else {
        // 使用内置规则解析
        result.codes = extractCodes(email.content);
        result.links = extractLinks(email.content);
    }
    
    console.log('邮件解析结果:', result);
    return result;
}

/**
 * 批量解析邮件
 * @param {Array} emails - 邮件列表
 * @param {Array} localRules - 本地解析规则
 * @param {Array} sharedRules - 共享解析规则
 * @returns {Array} 解析结果列表
 */
function parseEmails(emails, localRules = [], sharedRules = []) {
    return emails.map(email => parseEmail(email, localRules, sharedRules));
}

/**
 * 创建解析规则
 * @param {string} senderEmail - 发件人邮箱
 * @param {string} name - 规则名称
 * @param {string} sampleContent - 示例邮件内容
 * @param {string} sampleCode - 示例验证码
 * @param {string} sampleLink - 示例链接
 * @returns {Object} 解析规则对象
 */
function createParseRule(senderEmail, name, sampleContent, sampleCode = '', sampleLink = '') {
    const rule = {
        id: generateRuleId(),
        senderEmail: senderEmail.toLowerCase(),
        name: name,
        codePattern: '',
        linkPattern: '',
        isShared: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    // 根据示例内容和验证码生成匹配模式
    if (sampleCode && sampleContent) {
        rule.codePattern = generateCodePattern(sampleContent, sampleCode);
    }
    
    // 根据示例内容和链接生成匹配模式
    if (sampleLink && sampleContent) {
        rule.linkPattern = generateLinkPattern(sampleContent, sampleLink);
    }
    
    return rule;
}

/**
 * 生成规则ID
 * @returns {string} 规则ID
 */
function generateRuleId() {
    return `rule_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
}

/**
 * 根据示例生成验证码匹配模式
 * @param {string} content - 邮件内容
 * @param {string} code - 示例验证码
 * @returns {string} 正则表达式模式
 */
function generateCodePattern(content, code) {
    // 简单的模式生成逻辑
    const codeLength = code.length;
    const isNumeric = /^\d+$/.test(code);
    
    if (isNumeric) {
        return `\\d{${codeLength}}`;
    } else {
        return `[A-Z0-9]{${codeLength}}`;
    }
}

/**
 * 根据示例生成链接匹配模式
 * @param {string} content - 邮件内容
 * @param {string} link - 示例链接
 * @returns {string} 正则表达式模式
 */
function generateLinkPattern(content, link) {
    try {
        const url = new URL(link);
        const domain = url.hostname.replace(/\./g, '\\.');
        return `https?://${domain}[^\\s<>"']*`;
    } catch {
        return 'https?://[^\\s<>"\']+';
    }
}

/**
 * 测试解析规则
 * @param {Object} rule - 解析规则
 * @param {string} testContent - 测试内容
 * @returns {Object} 测试结果
 */
function testParseRule(rule, testContent) {
    const testEmail = {
        from: rule.senderEmail,
        content: testContent,
        subject: '测试邮件',
        messageId: 'test_' + Date.now()
    };
    
    return parseEmail(testEmail, [rule], []);
}

// 导出解析工具函数
window.TempMailParser = {
    extractCodes,
    extractLinks,
    findMatchingRule,
    parseEmail,
    parseEmails,
    createParseRule,
    testParseRule,
    generateRuleId,
    BUILTIN_CODE_PATTERNS,
    BUILTIN_LINK_PATTERNS
};
