/**
 * 本地存储工具模块
 * 负责管理插件的本地数据存储
 */

// 存储键名常量
const STORAGE_KEYS = {
    USER_ID: 'tempmail_user_id',
    CURRENT_EMAIL: 'tempmail_current_email',
    EMAIL_HISTORY: 'tempmail_email_history',
    LOCAL_PARSE_RULES: 'tempmail_local_parse_rules',
    USER_SETTINGS: 'tempmail_user_settings',
    LAST_CHECK_TIME: 'tempmail_last_check_time',
    CACHED_EMAILS: 'tempmail_cached_emails'
};

// 默认设置
const DEFAULT_SETTINGS = {
    domain: 'shengchai.dpdns.org',
    autoRefresh: true,
    refreshInterval: 5000, // 5秒
    maxEmailHistory: 50,
    enableNotifications: true,
    theme: 'light'
};

/**
 * 生成唯一用户ID
 * @returns {string} 用户ID（时间戳+随机数）
 */
function generateUserId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${timestamp}_${random}`;
}

/**
 * 获取或创建用户ID
 * @returns {Promise<string>} 用户ID
 */
async function getUserId() {
    try {
        const result = await chrome.storage.local.get(STORAGE_KEYS.USER_ID);
        if (result[STORAGE_KEYS.USER_ID]) {
            return result[STORAGE_KEYS.USER_ID];
        }
        
        // 创建新的用户ID
        const newUserId = generateUserId();
        await chrome.storage.local.set({ [STORAGE_KEYS.USER_ID]: newUserId });
        console.log('创建新用户ID:', newUserId);
        return newUserId;
    } catch (error) {
        console.error('获取用户ID失败:', error);
        // 降级到sessionStorage
        let userId = sessionStorage.getItem(STORAGE_KEYS.USER_ID);
        if (!userId) {
            userId = generateUserId();
            sessionStorage.setItem(STORAGE_KEYS.USER_ID, userId);
        }
        return userId;
    }
}

/**
 * 保存当前邮箱信息
 * @param {Object} emailInfo - 邮箱信息
 */
async function saveCurrentEmail(emailInfo) {
    try {
        await chrome.storage.local.set({ [STORAGE_KEYS.CURRENT_EMAIL]: emailInfo });
        console.log('保存当前邮箱:', emailInfo);
    } catch (error) {
        console.error('保存当前邮箱失败:', error);
        localStorage.setItem(STORAGE_KEYS.CURRENT_EMAIL, JSON.stringify(emailInfo));
    }
}

/**
 * 获取当前邮箱信息
 * @returns {Promise<Object|null>} 邮箱信息
 */
async function getCurrentEmail() {
    try {
        const result = await chrome.storage.local.get(STORAGE_KEYS.CURRENT_EMAIL);
        return result[STORAGE_KEYS.CURRENT_EMAIL] || null;
    } catch (error) {
        console.error('获取当前邮箱失败:', error);
        const stored = localStorage.getItem(STORAGE_KEYS.CURRENT_EMAIL);
        return stored ? JSON.parse(stored) : null;
    }
}

/**
 * 保存邮件到历史记录
 * @param {Array} emails - 邮件列表
 */
async function saveEmailsToHistory(emails) {
    try {
        const result = await chrome.storage.local.get(STORAGE_KEYS.EMAIL_HISTORY);
        const history = result[STORAGE_KEYS.EMAIL_HISTORY] || [];
        
        // 合并新邮件，避免重复
        const existingIds = new Set(history.map(email => email.messageId));
        const newEmails = emails.filter(email => !existingIds.has(email.messageId));
        
        const updatedHistory = [...newEmails, ...history];
        
        // 限制历史记录数量
        const settings = await getUserSettings();
        const maxHistory = settings.maxEmailHistory || DEFAULT_SETTINGS.maxEmailHistory;
        const trimmedHistory = updatedHistory.slice(0, maxHistory);
        
        await chrome.storage.local.set({ [STORAGE_KEYS.EMAIL_HISTORY]: trimmedHistory });
        console.log(`保存邮件历史: ${newEmails.length} 新邮件, 总计 ${trimmedHistory.length} 邮件`);
    } catch (error) {
        console.error('保存邮件历史失败:', error);
    }
}

/**
 * 获取邮件历史记录
 * @returns {Promise<Array>} 邮件历史
 */
async function getEmailHistory() {
    try {
        const result = await chrome.storage.local.get(STORAGE_KEYS.EMAIL_HISTORY);
        return result[STORAGE_KEYS.EMAIL_HISTORY] || [];
    } catch (error) {
        console.error('获取邮件历史失败:', error);
        return [];
    }
}

/**
 * 保存本地解析规则
 * @param {Array} rules - 解析规则列表
 */
async function saveLocalParseRules(rules) {
    try {
        await chrome.storage.local.set({ [STORAGE_KEYS.LOCAL_PARSE_RULES]: rules });
        console.log('保存本地解析规则:', rules.length, '条');
    } catch (error) {
        console.error('保存本地解析规则失败:', error);
        localStorage.setItem(STORAGE_KEYS.LOCAL_PARSE_RULES, JSON.stringify(rules));
    }
}

/**
 * 获取本地解析规则
 * @returns {Promise<Array>} 解析规则列表
 */
async function getLocalParseRules() {
    try {
        const result = await chrome.storage.local.get(STORAGE_KEYS.LOCAL_PARSE_RULES);
        return result[STORAGE_KEYS.LOCAL_PARSE_RULES] || [];
    } catch (error) {
        console.error('获取本地解析规则失败:', error);
        const stored = localStorage.getItem(STORAGE_KEYS.LOCAL_PARSE_RULES);
        return stored ? JSON.parse(stored) : [];
    }
}

/**
 * 保存用户设置
 * @param {Object} settings - 用户设置
 */
async function saveUserSettings(settings) {
    try {
        const currentSettings = await getUserSettings();
        const updatedSettings = { ...currentSettings, ...settings };
        await chrome.storage.local.set({ [STORAGE_KEYS.USER_SETTINGS]: updatedSettings });
        console.log('保存用户设置:', updatedSettings);
    } catch (error) {
        console.error('保存用户设置失败:', error);
        localStorage.setItem(STORAGE_KEYS.USER_SETTINGS, JSON.stringify(settings));
    }
}

/**
 * 获取用户设置
 * @returns {Promise<Object>} 用户设置
 */
async function getUserSettings() {
    try {
        const result = await chrome.storage.local.get(STORAGE_KEYS.USER_SETTINGS);
        return { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.USER_SETTINGS] };
    } catch (error) {
        console.error('获取用户设置失败:', error);
        const stored = localStorage.getItem(STORAGE_KEYS.USER_SETTINGS);
        return stored ? { ...DEFAULT_SETTINGS, ...JSON.parse(stored) } : DEFAULT_SETTINGS;
    }
}

/**
 * 清理所有存储数据
 */
async function clearAllData() {
    try {
        await chrome.storage.local.clear();
        console.log('清理所有存储数据完成');
    } catch (error) {
        console.error('清理存储数据失败:', error);
        // 降级清理localStorage
        Object.values(STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
            sessionStorage.removeItem(key);
        });
    }
}

/**
 * 获取存储使用情况
 * @returns {Promise<Object>} 存储使用情况
 */
async function getStorageUsage() {
    try {
        const usage = await chrome.storage.local.getBytesInUse();
        return {
            used: usage,
            quota: chrome.storage.local.QUOTA_BYTES,
            percentage: (usage / chrome.storage.local.QUOTA_BYTES * 100).toFixed(2)
        };
    } catch (error) {
        console.error('获取存储使用情况失败:', error);
        return { used: 0, quota: 0, percentage: 0 };
    }
}

// 导出存储工具函数
window.TempMailStorage = {
    getUserId,
    saveCurrentEmail,
    getCurrentEmail,
    saveEmailsToHistory,
    getEmailHistory,
    saveLocalParseRules,
    getLocalParseRules,
    saveUserSettings,
    getUserSettings,
    clearAllData,
    getStorageUsage,
    STORAGE_KEYS,
    DEFAULT_SETTINGS
};
