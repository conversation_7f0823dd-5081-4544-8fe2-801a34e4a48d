
## 临时邮箱服务项目需求说明

### 背景

本项目旨在为用户提供一个**一次性临时邮箱服务**，以便于在注册第三方平台时接收验证码。该服务利用用户已有的域名和126邮箱的邮件转发功能，结合浏览器插件和云服务器程序实现。

1.  **域名与邮件转发配置[需求说明mp.md](%E9%9C%80%E6%B1%82%E8%AF%B4%E6%98%8Emp.md)：** 用户已拥有注册域名（例如 `shengchai.dpdns.org`），并已在126邮箱中配置了邮件转发服务，将该域名的所有邮件转发至指定的中转邮箱（例如 `<EMAIL>`）。
2.  **云服务器准备：** 已建立一台云服务器（Ubuntu Server 22.04 LTS 64bit，部署1Panel Linux 面板），但尚未进行任何程序部署和配置。

-----

### 核心功能与流程

本项目将开发一个**Edge浏览器插件**和一个**云服务器端程序**。

#### 1\. 插件端功能 (Edge 浏览器插件)

  * **临时邮箱生成：** 在本地浏览器中，生成一个以用户指定域名（`shengchai.dpdns.org`）为后缀的随机字符串邮箱地址。随机字符串长度范围为 8 到 12 位。
  * **信息传输：** 将生成的临时邮箱地址及其生成时间，通过安全通道（API）发送至后端云服务器。
  * **邮件数据显示：** 接收来自云服务器的邮件数据，并清晰地展示给用户。
  * **验证码/链接提取：** 根据用户自定义或共享的解析规则，自动从邮件内容中提取验证码或链接。如果**没有**符合的解析规则，插件将**完整展示邮件原文**，而用于显示解析出的链接和验证码的区域将**留空**。
  * **解析规则管理：**
      * **规则创建：** 用户可输入目标平台的发件人邮箱（例如 `<EMAIL>`）、提供一封包含验证码/链接的完整邮件示例，并手动提取出验证码示例（例如 `986558`），用于生成解析模式。
      * **规则存储与共享：** 用户可选择将解析规则保存到本地浏览器（仅供当前用户使用）或上传至云服务器（共享给所有用户）。
      * **规则应用：** 插件将优先使用本地解析规则，若无匹配则尝试使用云服务器上的共享规则，根据收到的邮件地址自动进行解析。

#### 2\. 服务器端功能 (云服务器部署程序)

  * **邮件接收：** 运行部署的程序，通过 POP3/SMTP 服务连接中转邮箱 `<EMAIL>`。
  * **邮件过滤与转发：** 仅获取并处理在中转邮箱中，“临时邮箱生成时间”之后收到的邮件。这些邮件将是转发自用户生成的临时邮箱的邮件。
  * **邮件数据返回：** 将过滤后的邮件数据（不包括历史邮件）返回给请求的浏览器插件。
  * **共享解析规则存储：** 存储用户选择共享的解析规则，并提供给插件端查询。

-----

### 架构与资源优化要求

1.  **代码分离：** 将插件代码和服务器程序代码分别放置在 `plugin` 和 `mailexe` 两个独立的文件夹中，以保持代码库的整洁性。
2.  **服务器轻量化：** 云服务器资源有限，服务器端程序**仅**负责邮件接收和共享解析规则的存储与分发。邮件内容的解析（验证码/链接提取）功能**全部由插件端完成**，以最大限度地减少服务器资源占用。
3.  **全局资源节约：**
      * **按需获取邮件：** 服务器只获取“邮箱生成时间”之后的邮件，不获取、不解析之前的历史邮件。
      * **无用户数据持久化：** 除了共享的解析规则，后端服务器不存储任何用户数据或邮件内容。所有相关数据（如邮箱名称、生成时间等）仅作为临时变量在每次请求处理期间使用，请求完成后即销毁。
      * **非登录模式：** 优先采用最快速、最少资源的方式实现功能，无需用户登录服务器。
4.  **并发处理：** 该服务支持多用户并发使用。每个用户将拥有独立的临时邮箱，并通过用户ID（时间戳 + 随机数组合）进行数据隔离。可以考虑采用轻量级队列机制，以避免高并发场景下可能出现的问题。
5.  **安全性：**
      * 插件连接云服务的 **API 密钥**可直接硬编码在插件代码中。
      * 后端 API 权限需严格控制：API 仅限于返回邮件数据，禁止任何敏感操作（如删除、修改邮件）。
6.  **效率：** 期望用户在 **5 秒内**能够获取到生成的临时邮箱所接收到的邮件。
7.  **插件图标：** 插件图标文件位于 `plugin/icons/icon.png` 路径下。

-----

### 配置信息

1.  **POP3/SMTP 服务器配置:**
      * POP3 服务器地址: `pop.126.com`
      * POP3 端口: `995` (SSL/TLS 加密连接)
      * SMTP 服务器地址: `smtp.126.com`
      * SMTP 端口: `465` (SSL/TLS 加密连接)
2.  **中转邮箱配置:**
      * 中转邮箱地址: `<EMAIL>`
      * 中转邮箱授权码: `TQVTv7gUbCGy9eBR` (此授权码用于通过 POP3/SMTP 协议登录邮箱，必须安全存储和使用，不应明文显示)
      * 授权方式: POP3/SMTP 服务
3.  **服务器信息:**
      * 操作系统: Ubuntu Server 22.04 LTS 64bit
      * 面板: 1Panel Linux 面板
      * 云服务商: 腾讯云
      * IP地址: IPv4(公) `************`

-----

### 插件、服务器的事件发生、数据流转关系

#### 整体架构概览

```mermaid
graph TD
    A[用户] -->|打开Edge浏览器插件| B(Edge浏览器插件)
    B -->|生成临时邮箱 (本地)| C{临时邮箱生成}
    C -->|发送临时邮箱 & 生成时间| D[云服务器 (后端程序)]
    D -->|根据时间连接126邮箱 (POP3)| E(126邮箱 - <EMAIL>)
    E -->|收取新邮件| D
    D -->|返回邮件数据 (API)| B
    B -->|显示邮件 & 解析 (本地)| F{邮件显示 & 解析}
    F -->|提取验证码/链接 或 完整显示原文| A
    B -->|用户创建/更新解析规则| G{解析规则管理}
    G -->|选择共享| D
    G -->|不共享| B
    D -->|存储/提供共享规则| H[共享解析规则数据库/文件]
    B -->|查询解析规则| H
```

#### 事件发生与数据流转详细说明

1.  **临时邮箱生成与邮件获取流程**

      * **事件：用户打开 Edge 浏览器插件。**

          * **插件：**
              * 在用户界面上显示生成临时邮箱的选项。
              * 用户点击“生成”按钮。
              * **本地生成**一个随机字符串（8-12位），并拼接上用户配置的域名 (`shengchai.dpdns.org`)，例如 `<EMAIL>`。
              * 记录该临时邮箱的**生成时间**。
              * 将**临时邮箱地址**和**生成时间**通过 HTTPS POST 请求发送到云服务器的 API 接口（例如 `https://************/api/generate-email`）。此请求中可能包含一个用户ID（时间戳+随机数组合），用于后续的数据隔离。

      * **事件：云服务器接收到临时邮箱生成请求。**

          * **云服务器 (后端程序)：**
              * 接收到来自插件的临时邮箱地址、生成时间和用户ID。
              * **临时存储**这些信息（例如在内存中，与用户ID关联）。
              * **启动邮件收取流程：**
                  * 使用 POP3 协议连接 `pop.126.com:995`。
                  * 使用中转邮箱 `<EMAIL>` 和授权码 `TQVTv7gUbCGy9eBR` 进行认证。
                  * **核心逻辑：** 遍历中转邮箱中的邮件，**只获取邮件发送时间晚于**插件发送过来的“临时邮箱生成时间”的邮件。
                  * 对获取到的每封邮件，检查其收件人是否包含当前用户生成的临时邮箱地址。
                  * 将符合条件的邮件内容（包括发件人、主题、邮件正文等）打包成数据结构。
                  * 通过 HTTPS GET 请求将邮件数据返回给请求的插件（例如 `https://************/api/get-emails?userId=xxx`）。

      * **事件：插件接收到云服务器返回的邮件数据。**

          * **插件：**
              * 接收到云服务器返回的邮件数据。
              * 在插件界面上**显示**这些邮件。
              * **触发解析逻辑：**
                  * 根据每封邮件的**发件人邮箱地址**，在**本地存储的解析规则**中查找匹配项。
                  * 如果本地没有匹配项，则向云服务器请求**共享解析规则**（例如 `https://************/api/get-shared-rules`），并在共享规则中查找匹配项。
                  * 如果找到匹配的解析规则，则根据该规则从邮件正文中**自动提取**验证码或链接。将提取到的验证码或链接在插件界面上高亮显示或单独展示。
                  * 如果**没有**找到匹配的解析规则，插件将**完整展示邮件原文**，而解析出的链接和验证码区域则**保持空着**。

2.  **解析规则管理流程**

      * **事件：用户在插件中创建/修改解析规则。**

          * **插件：**
              * 提供用户输入界面，包括：
                  * 目标平台发件人邮箱 (`<EMAIL>`)。
                  * 完整的邮件示例内容。
                  * 手动提取的验证码示例 (`986558`)。
              * 用户点击“保存”或“共享”按钮。

      * **事件：用户选择“不共享”解析规则。**

          * **插件：**
              * 将解析规则（包含发件人邮箱、解析模式等）**本地存储**在浏览器存储（如 `localStorage` 或 `IndexedDB`）中，仅供当前浏览器实例使用。

      * **事件：用户选择“共享”解析规则。**

          * **插件：**
              * 将解析规则数据（发件人邮箱、解析模式）通过 HTTPS POST 请求发送到云服务器的 API 接口（例如 `https://************/api/upload-shared-rule`）。

      * **事件：云服务器接收到共享解析规则上传请求。**

          * **云服务器 (后端程序)：**
              * 接收到来自插件的解析规则数据。
              * 对数据进行验证和清洗（可选）。
              * 将解析规则**持久化存储**在服务器端的数据库或文件中（例如，一个 JSON 文件或小型数据库），以便所有用户访问。

3.  **并发处理与资源销毁**

      * **数据隔离：** 每个用户在启动插件时，插件生成一个唯一的**用户ID**（例如，基于时间戳和随机数），并在所有与服务器的交互中带上这个ID。服务器端根据这个用户ID来区分不同的用户请求，从而实现数据隔离。
      * **临时变量销毁：** 服务器在处理完一个用户的邮件获取请求并返回数据后，与该用户请求相关的临时邮箱、生成时间等内存变量数据**立即销毁**，不进行持久化存储。
      * **轻量队列：** 如果预期并发量很高，可以在服务器端引入一个**轻量级的任务队列**。当多个用户同时请求邮件获取时，可以将邮件收取任务放入队列中，服务器端程序按顺序或优先级处理，避免直接并发操作邮箱，从而控制资源使用和保证稳定性。

