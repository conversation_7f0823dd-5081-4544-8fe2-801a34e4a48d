{"groups": [{"name": "mail", "type": "com.mailservice.config.MailConfig", "sourceType": "com.mailservice.config.MailConfig"}], "properties": [{"name": "mail.connection-timeout", "type": "java.lang.Integer", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": 10000}, {"name": "mail.pop3-host", "type": "java.lang.String", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": "pop.126.com"}, {"name": "mail.pop3-port", "type": "java.lang.Integer", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": 995}, {"name": "mail.pop3-ssl", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": true}, {"name": "mail.read-timeout", "type": "java.lang.Integer", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": 10000}, {"name": "mail.smtp-host", "type": "java.lang.String", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": "smtp.126.com"}, {"name": "mail.smtp-port", "type": "java.lang.Integer", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": 465}, {"name": "mail.smtp-ssl", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": true}, {"name": "mail.transfer-email", "type": "java.lang.String", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": "<EMAIL>"}, {"name": "mail.transfer-password", "type": "java.lang.String", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": "TQVTv7gUbCGy9eBR"}, {"name": "mail.user-domain", "type": "java.lang.String", "sourceType": "com.mailservice.config.MailConfig", "defaultValue": "shengchai.dpdns.org"}], "hints": []}